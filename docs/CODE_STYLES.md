# Code Style Guidelines

## Formatting Rules

### Indentation & Spacing

-   Use 4 spaces for indentation, not tabs
-   No enforced maximum line length (printWidth: false)
-   Remove trailing whitespace
-   Add a blank line at end of files
-   Use LF (`\n`) line endings, not CRLF
-   Place spaces inside object braces: `{ like: this }`
-   Add space before opening braces: `function name() {`
-   Enable block spacing for consistent formatting

### Punctuation & Symbols

-   No semicolons at the end of statements
-   Use single quotes (`'`) for strings in JS/TS
-   Use double quotes (`"`) for JSX attributes
-   Use trailing commas in ES5 style:
    -   Always for multiline arrays and objects
    -   Never for imports/exports
    -   Never for function parameters
-   Always use parentheses with arrow functions, even for single parameters
-   Use "one true brace style" (1tbs): opening brace on same line
-   Closing bracket on new line (bracketSameLine: false)
-   Empty arrow function bodies must be on single line: `() => {}` not `() => {\n}`

### Line Breaking & Padding

-   Add blank lines before and after major code blocks
-   Add blank line before `return` statements
-   Always blank line before and after: `class`, `interface`, `function`, `if`, `for`, `while`, `switch`, `try`
-   No blank lines between `case` statements in `switch`
-   Consistent object and array formatting based on complexity
-   Objects with fewer properties stay single line, complex objects use multi-line format with minimum properties threshold

## JavaScript & TypeScript

### Import Organization

Group imports in strict order:

1. Node.js built-in modules (with `node:` prefix)
2. External libraries (alphabetical)
3. Side-effect imports (`import 'module'`)
4. Internal modules (by proximity: `../`, `./`)

### Import Rules

-   Remove unused imports automatically
-   Keep import statements at the top of the file
-   Keep import in one line for each import (no line breaks in import statements)
-   Keep export statements on one line without line breaks
-   Use type-only imports: `import type { Type }` for types only
-   Prefer inline type imports: `import { type Type, value }` when mixing
-   No import type side effects

### Type Safety

-   Use TypeScript's strict type checking
-   Define clear types for functions and variables
-   Only specify return types for complex types or when the return type is not obvious from the code
-   Add accessibility modifier on class properties and methods
-   Prefer using interface instead of type for TypeScript interfaces
-   No `any` type (prefer `unknown`)
-   Use utility types for type manipulation (`Pick`, `Omit`, `Partial`)
-   For nested interfaces, prefer extracting them into separate interfaces rather than inline nesting for improved reusability and readability

### Function & Variable Rules

-   Keep return statements clear and explicit
-   Function length: maximum 30 lines recommended
-   Nesting depth: maximum 3 levels deep
-   Unused parameters: prefix with underscore (`_error`, `_unused`)
-   Use descriptive names that indicate purpose
-   For simple functions, prefer arrow functions with direct return instead of intermediate variables
-   Keep entire return expressions on one line when possible
-   For complex inline objects, extract them to variables for better readability
-   Keep function parameters on the same line as the function declaration when reasonable
-   Use arrow functions with object returns for simple data transformations

## Naming Conventions

### Standard Conventions

-   Variables and functions: camelCase
-   Classes and Components: PascalCase
-   Global constants: UPPERCASE_SNAKE_CASE
-   Files: kebab-case for regular files, PascalCase for component files
-   Directories: kebab-case grouped by functionality

## Code Organization

### File Structure

1. Imports (following grouping rules)
2. Type definitions and interfaces
3. Constants and configuration
4. Implementation (functions, classes)
5. Exports (prefer named exports, organized alphabetically)

### Class Organization

Structure class members in this order:

1. Public properties
2. Protected properties
3. Private properties (prefer `#privateField` syntax for private fields)
4. Constructor
5. Static methods
6. Instance methods (public → protected → private)

**Class Formatting Rules:**

-   Add blank lines between different access modifier groups (public, protected, private)
-   Group properties by access modifier with spacing between groups
-   For constructors with few parameters, prefer inline access modifiers: `public constructor(private readonly config: Config) {}`
