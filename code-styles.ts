// Code Style Validation File - Demonstrates all rules from docs/CODE_STYLES.md

// Import Organization Examples (Rule: Group imports in strict order)
import { readFileSync } from 'node:fs'
import { join } from 'node:path'

import axios from 'axios'
import lodash from 'lodash'

import 'reflect-metadata'

import { type DatabaseConfig, UserService } from '../services/user-service'
import { Logger } from './logger'

// Type Definitions (Rule: Prefer interface over type, extract nested interfaces)
interface UserProfile {
    id: string
    name: string
    email: string
    preferences: UserPreferences
}

interface UserPreferences {
    theme: 'light' | 'dark'
    notifications: boolean
    language: string
}

interface ApiResponse<T> {
    data: T
    status: number
    message: string
}

// Constants (Rule: UPPERCASE_SNAKE_CASE for global constants)
const MAX_RETRY_ATTEMPTS = 3
const DEFAULT_TIMEOUT = 5000
const API_BASE_URL = 'https://api.example.com'

// Utility Types Examples (Rule: Use utility types for type manipulation)
type PartialUser = Partial<UserProfile>
type UserEmail = Pick<UserProfile, 'email'>
type UserWithoutId = Omit<UserProfile, 'id'>

// Function Examples (Rule: camelCase naming, clear return types when needed)
function calculateTotal(items: number[]): number {
    return items.reduce((sum, item) => sum + item, 0)
}

// Arrow function with direct return (Rule: prefer direct return for simple functions)
const formatUserName = (user: UserProfile): string => `${user.name} <${user.email}>`

// Complex object extraction (Rule: extract complex inline objects to variables)
function createApiRequest(endpoint: string, data: unknown) {
    const requestConfig = {
        url: `${API_BASE_URL}${endpoint}`,
        method: 'POST',
        timeout: DEFAULT_TIMEOUT,
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        data,
    }

    return axios(requestConfig)
}

// Error Handling Example (Rule: descriptive error handling)
async function fetchUserData(userId: string): Promise<UserProfile> {
    try {
        const response = await axios.get<ApiResponse<UserProfile>>(`/users/${userId}`)

        return response.data.data
    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(`Failed to fetch user ${userId}: ${error.message}`)
        }

        throw new Error(`Unexpected error fetching user ${userId}`)
    }
}

// Class Organization Example (Rule: proper member organization and spacing)
class UserManager {
    public readonly maxUsers: number

    protected cache: Map<string, UserProfile>

    #apiKey: string
    #retryCount: number

    public constructor(
        private readonly config: DatabaseConfig,
        private readonly logger: Logger
    ) {
        this.maxUsers = config.maxConnections
        this.cache = new Map()
        this.#apiKey = config.apiKey
        this.#retryCount = 0
    }

    static createDefault(): UserManager {
        const defaultConfig: DatabaseConfig = {
            host: 'localhost',
            port: 5432,
            maxConnections: 100,
            apiKey: 'default-key',
        }

        return new UserManager(defaultConfig, new Logger())
    }

    public async getUser(id: string): Promise<UserProfile | null> {
        if (this.cache.has(id)) {
            return this.cache.get(id) || null
        }

        try {
            const user = await this.#fetchFromApi(id)
            this.cache.set(id, user)

            return user
        } catch (error) {
            this.logger.error(`Failed to get user ${id}`, error)

            return null
        }
    }

    protected clearCache(): void {
        this.cache.clear()
        this.logger.info('User cache cleared')
    }

    #fetchFromApi(id: string): Promise<UserProfile> {
        return fetchUserData(id)
    }
}

// Switch Statement Example (Rule: no blank lines between cases)
function getStatusMessage(status: number): string {
    switch (status) {
    case 200:
        return 'Success'
    case 400:
        return 'Bad Request'
    case 401:
        return 'Unauthorized'
    case 404:
        return 'Not Found'
    case 500:
        return 'Internal Server Error'
    default:
        return 'Unknown Status'
    }
}

// Array and Object Formatting Examples (Rule: consistent formatting based on complexity)
const simpleConfig = { host: 'localhost', port: 3000 }

const complexConfig = {
    database: {
        host: 'localhost',
        port: 5432,
        name: 'myapp',
        ssl: true,
    },
    redis: {
        host: 'localhost',
        port: 6379,
        password: 'secret',
    },
    logging: {
        level: 'info',
        format: 'json',
        outputs: ['console', 'file'],
    },
}

// Unused Parameters Example (Rule: prefix with underscore)
function processData(data: unknown[], _metadata?: Record<string, unknown>): unknown[] {
    return data.filter(item => item !== null)
}

// Type-only Import Example (already shown above with DatabaseConfig)
// Inline type import example would be: import { type Config, createService } from './service'

// Export Organization (Rule: alphabetical organization, single-line exports)
export { type ApiResponse, type UserPreferences, type UserProfile }
export { calculateTotal, createApiRequest, fetchUserData, formatUserName, getStatusMessage, processData, UserManager }
